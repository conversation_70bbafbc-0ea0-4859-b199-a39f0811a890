package com.longsheng.tools.open;

import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.longsheng.tools.common.gm.GmPayNotifyParam;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.common.utils.StringUtils;
import com.longsheng.tools.modules.appEntry.entity.PromoteBusinessParam;
import com.longsheng.tools.modules.appEntry.service.CashWithdrawService;
import com.longsheng.tools.modules.appEntry.service.PromoteBusinessParamService;
import com.longsheng.tools.modules.express.entity.WechatCrashLog;
import com.longsheng.tools.modules.express.params.QueryPriceCommand;
import com.longsheng.tools.modules.express.service.ExpPriceService;
import com.longsheng.tools.modules.express.service.ExpUserPriceService;
import com.longsheng.tools.modules.express.service.WechatCrashLogService;
import com.longsheng.tools.modules.express.vo.PriceDto;
import com.longsheng.tools.modules.express.vo.WechatCrashLogDTO;
import com.longsheng.tools.modules.order.service.PaymentRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/open")
public class OpenApiController {


    @Resource
    private PaymentRecordService paymentRecordService;
    @Resource
    PromoteBusinessParamService promoteBusinessParamService;

    @Resource
    CashWithdrawService withdrawService;

    @Resource
    ExpPriceService expPriceService;

    @Resource
    ExpUserPriceService expUserPriceService;

    @Resource
    private WechatCrashLogService wechatCrashLogService;

    @PostMapping("/aliPayNotify")
    public RES aliPayNotify(HttpServletRequest request) throws AlipayApiException {
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = iter.next();
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            params.put(name, valueStr);
        }
        //切记alipayPublicCertPath是支付宝公钥证书路径，请去open.alipay.com对应应用下载。
        //boolean AlipaySignature.rsaCertCheckV1(Map<String, String> params, String publicKeyCertPath, String charset,String signType)
        boolean flag = AlipaySignature.rsaCertCheckV1(params, "alipayPublicKey_RSA2.txt", CharsetUtil.UTF_8, "RSA2");
        return RES.ok();
    }

    @PostMapping("/wechatPayNotify")
    public String wechatPayNotify(@RequestBody String body) {
        log.info("微信支付回调：{}", body);
        paymentRecordService.wechatPayCallback(body);
        return "success";
    }

    //    用于查找应用标题
    @GetMapping("/queryWxMpBase/{id}")
    public RES queryWxMpBase(@PathVariable("id") Long id) {
        PromoteBusinessParam byId = promoteBusinessParamService.getById(id);
        if (ObjectUtils.isEmpty(byId)) {
            return RES.no("promotionBusinessParamId参数错误");
        }
        return RES.ok((Object) byId.getApplicationName());
    }

    @PostMapping("/gmNotify/{requestId}")
    public String gmNotify(@PathVariable(name = "requestId") String requestId, @RequestBody GmPayNotifyParam param) {
        log.info("接收到工猫灵活用工回调：requestId:{}，param:{}", requestId, JSONObject.toJSONString(param));
        withdrawService.gmNotify(param);
        return "{\"success\":true}";
    }

    @PostMapping("/queryPrice")
    public RES queryPrice(@RequestBody QueryPriceCommand param) {
        PriceDto dto = expPriceService.queryPrice(param);
        return RES.ok(dto);
    }

    @PostMapping("/queryPriceMsg")
    public RES queryPriceMsg(@RequestBody QueryPriceCommand param) {
        if (param.getQueryType() == null || param.getQueryType() == 1) {
            PriceDto dto = expPriceService.queryPrice(param);
            String res = "%s\n" +
                    "%s\n" +
                    "%s\n";
            String head = "";
            String body = "";
            String foot = "";
            if (StringUtils.isNotBlank(dto.getTmpHead())){
                head = expUserPriceService.convertMsg(dto.getTmpHead(), dto);
            }
            if (StringUtils.isNotBlank(dto.getTmpBody())){
                body = expUserPriceService.convertMsg(dto.getTmpBody(), dto);
            }
            if (StringUtils.isNotBlank(dto.getTmpFoot())){
                foot = expUserPriceService.convertMsg(dto.getTmpFoot(), dto);
            }
            return RES.ok(String.format(res, head, body, foot));
        }else {

        }

    }

    @GetMapping("/queryGroups")
    public RES queryGroups(){
        return RES.ok(expPriceService.getGroupList());
    }

    @PostMapping("/wechat/notifyError")
    public RES wechatNotifyError(@RequestBody WechatCrashLogDTO dto){
        WechatCrashLog log = new WechatCrashLog();
        log.setIp(dto.getIp());
        log.setErrorMsg(dto.getError());
        log.setCreateDate(new Date());
        wechatCrashLogService.save(log);
        return RES.ok();
    }

}
