package com.longsheng.tools.modules.express.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.longsheng.tools.common.exception.CustomException;
import com.longsheng.tools.common.redis.RedisUtil;
import com.longsheng.tools.modules.express.entity.ExpPriceEntity;
import com.longsheng.tools.modules.express.entity.ExpPricePartitionEntity;
import com.longsheng.tools.modules.express.entity.ExpUser;
import com.longsheng.tools.modules.express.entity.ExpUserPrice;
import com.longsheng.tools.modules.express.mapper.ExpPriceMapper;
import com.longsheng.tools.modules.express.mapper.ExpPricePartitionMapper;
import com.longsheng.tools.modules.express.params.*;
import com.longsheng.tools.modules.express.service.ExpPriceService;
import com.longsheng.tools.modules.express.service.ExpProvinceService;
import com.longsheng.tools.modules.express.service.ExpUserPriceService;
import com.longsheng.tools.modules.express.service.ExpUserService;
import com.longsheng.tools.modules.express.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExpPriceServiceImpl implements ExpPriceService {


    @Autowired
    private ExpPriceMapper expPriceMapper;
    @Autowired
    private ExpPricePartitionMapper expPricePartitionMapper;
    @Autowired
    private ExpPriceService expPriceService;
    @Autowired
    private ExpProvinceService expProvinceService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ExpUserPriceService userPriceService;
    @Autowired
    private ExpUserService expUserService;

    /**
     * 价格分区缓存Key前缀
     */
    private static final String PRICE_PARTITION_KEY_PREFIX = "price:partition:";

    private static final String PRICE_PID_START_END_PREFIX = "PRICE:PID%s:";
    /**
     * 价格表缓存
     */
    private static final String PRICE_PID_START_END = "PRICE:PID%s:START%s:END%s";


    @Override
    public List<ExpPriceEntity> getPriceList(ExpPriceQuery query) {
        // 取价格表数据
        QueryWrapper<ExpPriceEntity> qwPrice = new QueryWrapper<>();
        qwPrice.like(StringUtils.isNotBlank(query.getName()), "name", query.getName());
        return expPriceMapper.selectList(qwPrice);
    }


    @Transactional
    @Override
    public void savePrice(ExpPriceCommand command) {
        ExpPriceEntity price = new ExpPriceEntity();
        price.setId(command.getId());
        price.setName(command.getName());
        price.setRemark(command.getRemark());
        price.setIsDetail(command.getIsDetail());
        price.setIsCommon(command.getIsCommon());
        price.setCompanyName(command.getCompanyName());
        price.setPaobi(command.getPaobi());
        price.setUserId(command.getUserId());
        if (price.getId() == null) {
            price.setCreateTime(LocalDateTime.now());
            expPriceMapper.insert(price);
        } else {
            price.setUpdateTime(LocalDateTime.now());
            expPriceMapper.updateById(price);
        }
        if (!command.getIsCommon()) {
            ExpUserPrice userPrice = new ExpUserPrice();
            userPrice.setUserId(command.getUserId());
            userPrice.setPriceId(price.getId());
            userPrice.setDiscount(command.getDiscount());
            userPriceService.saveRef(userPrice);
        } else {
            userPriceService.removeByUserAndPrice(command.getUserId(), command.getId());
        }
    }


    @Override
    public void createArea(ExpPartitionCommand command) {
        ExpPricePartitionEntity area = new ExpPricePartitionEntity();
        Integer isDetail = command.getIsDetail();
        if (isDetail == null) {
            isDetail = 0;
        }
        BeanUtils.copyProperties(command, area);
        if (isDetail == 0) {
            area.setDestCodes(JSONObject.toJSONString(command.getDestCodeList()));
            area.setOriginCodes(JSONObject.toJSONString(command.getOriginCodeList()));
        } else if (isDetail == 1) {
            //判断是1的情况这边去处理下那个省字段
            List<List<String>> destCodeListDetail = command.getDestCodeListDetail();
            List<List<String>> originCodeListDetail = command.getOriginCodeListDetail();
            ArrayList<String> arrDestCode = new ArrayList<>();
            for (List<String> strings : destCodeListDetail) {
                //如果说是这个字段的size等与1的时候说明是穿的省过来需要把所有的市加上
                int size = strings.size();
                if (size == 1) {
                    //查询他下面的所有市这个应该是省的信息
                    arrDestCode.addAll(expProvinceService.getCityByCode(strings.get(0)));
                } else {
                    String s = strings.get(1);
                    arrDestCode.add(s);
                }
            }
            area.setDestCodes(JSONObject.toJSONString(arrDestCode));
            ArrayList<String> arrOriginCode = new ArrayList<>();
            for (List<String> strings : originCodeListDetail) {
                //如果说是这个字段的size等与1的时候说明是穿的省过来需要把所有的市加上
                int size = strings.size();
                if (size == 1) {
                    //查询他下面的所有市
                    arrOriginCode.addAll(expProvinceService.getCityByCode(strings.get(0)));
                } else {
                    String s = strings.get(1);
                    arrOriginCode.add(s);
                }
            }
            area.setOriginCodes(JSONObject.toJSONString(arrOriginCode));
            area.setComputeSection(JSONObject.toJSONString(command.getPriceList()));
        } else {
            //判断是1的情况这边去处理下那个省字段
            area.setDestCodes(JSONObject.toJSONString(command.getDestCodeList()));
            area.setOriginCodes(JSONObject.toJSONString(command.getOriginCodeList()));
            area.setComputeSection(JSONObject.toJSONString(command.getPriceList()));
        }
        expPricePartitionMapper.insert(area);
        redisUtil.delete(PRICE_PARTITION_KEY_PREFIX + command.getPid());
        redisUtil.removeByPrefix(String.format(PRICE_PID_START_END_PREFIX, command.getPid()));

    }

    @Override
    public void modifyArea(ExpPartitionCommand command) {
        ExpPricePartitionEntity area = new ExpPricePartitionEntity();
        BeanUtils.copyProperties(command, area);
        Integer isDetail = command.getIsDetail();
        if (isDetail == 0) {
            area.setDestCodes(JSONObject.toJSONString(command.getDestCodeList()));
            area.setOriginCodes(JSONObject.toJSONString(command.getOriginCodeList()));

        } else if (isDetail == 1) {
            //判断是1的情况这边去处理下那个省字段
            List<List<String>> destCodeListDetail = command.getDestCodeListDetail();
            List<List<String>> originCodeListDetail = command.getOriginCodeListDetail();
            ArrayList<String> arrDestCode = new ArrayList<>();
            for (List<String> strings : destCodeListDetail) {
                //如果说是这个字段的size等与1的时候说明是穿的省过来需要把所有的市加上
                int size = strings.size();
                if (size == 1) {
                    //查询他下面的所有市
                    arrDestCode.addAll(expProvinceService.getCityByCode(strings.get(0)));
                } else {
                    String s = strings.get(1);

                    arrDestCode.add(s);
                }
            }
            area.setDestCodes(JSONObject.toJSONString(arrDestCode));
            ArrayList<String> arrOriginCode = new ArrayList<>();
            for (List<String> strings : originCodeListDetail) {
                //如果说是这个字段的size等与1的时候说明是穿的省过来需要把所有的市加上
                int size = strings.size();
                if (size == 1) {
                    //查询他下面的所有市
                    arrOriginCode.addAll(expProvinceService.getCityByCode(strings.get(0)));
                } else {
                    String s = strings.get(1);

                    arrOriginCode.add(s);
                }
            }
            area.setOriginCodes(JSONObject.toJSONString(arrOriginCode));
            area.setComputeSection(JSONObject.toJSONString(command.getPriceList()));
        } else {
            area.setDestCodes(JSONObject.toJSONString(command.getDestCodeList()));
            area.setOriginCodes(JSONObject.toJSONString(command.getOriginCodeList()));
            area.setComputeSection(JSONObject.toJSONString(command.getPriceList()));
        }
        area.setUpdateTime(LocalDateTime.now());
        expPricePartitionMapper.updateById(area);
        redisUtil.delete(PRICE_PARTITION_KEY_PREFIX + command.getPid());
        redisUtil.removeByPrefix(String.format(PRICE_PID_START_END_PREFIX, command.getPid()));
    }

    @Override
    public void deleteArea(long areaid) {
        ExpPricePartitionEntity expPricePartitionEntity = expPricePartitionMapper.selectById(areaid);
        if (expPricePartitionEntity != null) {
            expPricePartitionMapper.deleteById(areaid);
            redisUtil.delete(PRICE_PARTITION_KEY_PREFIX + expPricePartitionEntity.getPid());
            redisUtil.removeByPrefix(String.format(PRICE_PID_START_END_PREFIX, expPricePartitionEntity.getPid()));
        }
    }


    @Override
    public List<ExpPricePartitionDTO> getPartitionByList(long pid) {
        String resultStr = redisUtil.get(PRICE_PARTITION_KEY_PREFIX + pid);
        if (StrUtil.isNotBlank(resultStr)) {
            List<ExpPricePartitionDTO> result = null;
            try {
                result = JSONArray.parseArray(resultStr, ExpPricePartitionDTO.class);
            } catch (Exception e) {
                log.error("转换redis里查出来的价格分区表出现错误！pid:{}", pid);
            }
            if (result != null && !result.isEmpty()) {
                return result;
            }
        }
        QueryWrapper<ExpPricePartitionEntity> qw = new QueryWrapper<>();
        qw.eq("pid", pid);
        List<ExpPricePartitionEntity> list = expPricePartitionMapper.selectList(qw);
        if (list != null) {
            List<ExpPricePartitionDTO> collect = list.stream().map(x -> {
                ExpPricePartitionDTO dto = new ExpPricePartitionDTO();
                BeanUtils.copyProperties(x, dto);
                dto.setDestCodeList(JSONObject.parseArray(x.getDestCodes(), String.class));
                dto.setOriginCodeList(JSONObject.parseArray(x.getOriginCodes(), String.class));
                return dto;
            }).collect(Collectors.toList());
            redisUtil.set(PRICE_PARTITION_KEY_PREFIX + pid, JSONArray.toJSONString(collect));
            return collect;
        }
        return null;
    }

    private List<ArrayList<String>> dealDetail(List<String> arrOriginCode) {
        List<ArrayList<String>> arrayLists = new ArrayList<ArrayList<String>>();
        for (String s : arrOriginCode) {
            //查询省放入省的编码
            String provinceCode = expProvinceService.getProvinceCodeByCityCode(s);
            if (StringUtils.isNotBlank(provinceCode)) {
                ArrayList<String> itemArr = new ArrayList<>();
                itemArr.add(provinceCode);
                itemArr.add(s);
                arrayLists.add(itemArr);
            }
        }
        return arrayLists;
    }


    @Override
    public Page<ExpPricePartitionDTO> getPartitionPage(Long pid, ExpPriceQuery query) {
        //看看这个价格表示不是精确到市级
        ExpPriceEntity expPriceEntity = expPriceService.getById(pid);
        //看看是不是市到市的价格区间
        Integer isDetail = expPriceEntity.getIsDetail();


        QueryWrapper<ExpPricePartitionEntity> qw = new QueryWrapper<>();
        String name = query.getName();
        qw.eq("pid", pid);
        qw.like(StringUtils.isNotEmpty(name), "name", name);
        qw.like(StringUtils.isNotEmpty(query.getOriginCode()), "origin_codes", query.getOriginCode());
        qw.like(StringUtils.isNotEmpty(query.getDestCode()), "dest_codes", query.getDestCode());
        qw.orderByDesc("create_time");
        IPage<ExpPricePartitionEntity> iPage = new Page<>(query.getPageNo(), query.getPageSize());
        iPage = expPricePartitionMapper.selectPage(iPage, qw);
        List<ExpPricePartitionEntity> list = iPage.getRecords();
        if (list != null) {
            List<ExpPricePartitionDTO> collect = list.stream().map(x -> {
                ExpPricePartitionDTO dto = new ExpPricePartitionDTO();
                BeanUtils.copyProperties(x, dto);
                dto.setDestCodeList(JSONObject.parseArray(x.getDestCodes(), String.class));
                dto.setOriginCodeList(JSONObject.parseArray(x.getOriginCodes(), String.class));
                if (isDetail == 1) {
                    dto.setDataList(JSONObject.parseArray(x.getComputeSection(), DetailPartition.class));
                    //只查五个信息
                    //放入一个寄件Str
                    List<String> sendArr = JSONObject.parseArray(x.getOriginCodes(), String.class);
                    if (sendArr.size() >= 5) {
                        sendArr = sendArr.subList(0, 5);
                    }
                    ArrayList<String> sendStrArr = new ArrayList<>();
                    for (String s : sendArr) {
                        String nameItem = expProvinceService.getByCode(s);
                        if (nameItem != null) {
                            sendStrArr.add(nameItem);
                        }
                    }
                    dto.setSendStr(StringUtils.join(sendStrArr, "、"));
                    //放入一个收件Str
                    List<String> putArr = JSONObject.parseArray(x.getDestCodes(), String.class);
                    if (putArr.size() >= 5) {
                        putArr = putArr.subList(0, 5);
                    }
                    ArrayList<String> putStrArr = new ArrayList<>();
                    for (String s : putArr) {
                        String nameItem = expProvinceService.getByCode(s);
                        if (nameItem != null) {
                            putStrArr.add(nameItem);
                        }
                    }
                    dto.setPutStr(StringUtils.join(putStrArr, "、"));
                    //需要将这个code也给他返回
                    dto.setDestCodeList(JSONObject.parseArray(x.getDestCodes(), String.class));
                    dto.setOriginCodeList(JSONObject.parseArray(x.getOriginCodes(), String.class));
                    List<String> arrOriginCode = JSONObject.parseArray(x.getOriginCodes(), String.class);
                    List<String> arrDestCodes = JSONObject.parseArray(x.getDestCodes(), String.class);
                    dto.setOriginCodeListDetail(dealDetail(arrOriginCode));
                    dto.setDestCodeListDetail(dealDetail(arrDestCodes));

                }
                if (isDetail == 2) {
                    dto.setDataList(JSONObject.parseArray(x.getComputeSection(), DetailPartition.class));
                }
                return dto;
            }).collect(Collectors.toList());
            return new PageDTO<ExpPricePartitionDTO>(iPage.getSize(), iPage.getPages(), iPage.getTotal()).setRecords(collect);
        }

        return null;
    }

    @Override
    public Page<ExpPricePartitionDTO> shopPartitionPage(Long pid, Long channelId, String merchantId, ExpPriceQuery query) {
        //查询出来这个东西 他的首重和续重
        //查询除他的商户号id

        Page<ExpPricePartitionDTO> partitionPage = getPartitionPage(pid, query);
        List<ExpPricePartitionDTO> list = partitionPage.getRecords();
        for (ExpPricePartitionDTO expPricePartitionDTO : list) {
            Integer firstPrice = expPricePartitionDTO.getFirstPrice();
            Integer overPrice = expPricePartitionDTO.getOverPrice();
            Double firstWeight = expPricePartitionDTO.getFirstWeight();
            Double overWeight = expPricePartitionDTO.getOverWeight();
            List<String> sendArr = expPricePartitionDTO.getOriginCodeList();
            if (sendArr.size() >= 5) {
                sendArr = sendArr.subList(0, 5);
            }
            ArrayList<String> sendStrArr = new ArrayList<>();
            for (String s : sendArr) {
                String nameItem = expProvinceService.getByCode(s);
                if (nameItem != null) {
                    sendStrArr.add(nameItem);
                }
            }
            expPricePartitionDTO.setSendStr(StringUtils.join(sendStrArr, "、"));
            //放入一个收件Str
            List<String> putArr = expPricePartitionDTO.getDestCodeList();
            if (putArr.size() >= 5) {
                putArr = putArr.subList(0, 5);
            }
            ArrayList<String> putStrArr = new ArrayList<>();
            for (String s : putArr) {
                String nameItem = expProvinceService.getByCode(s);
                if (nameItem != null) {
                    putStrArr.add(nameItem);
                }
            }
            expPricePartitionDTO.setPutStr(StringUtils.join(putStrArr, "、"));
            List<DetailPartition> dataList = expPricePartitionDTO.getDataList();
            if (dataList == null || CollectionUtil.isEmpty(dataList)) {
                dataList = new ArrayList<DetailPartition>();
                DetailPartition detailPartition = new DetailPartition();
                detailPartition.setMinWeight(0.0D);
                detailPartition.setMaxWeight(9999D);
                detailPartition.setEveryPrice(overPrice);
                detailPartition.setOverWeight(overWeight);
                detailPartition.setFirstPrice(firstPrice);
                detailPartition.setFirstWeight(firstWeight);
                dataList.add(detailPartition);
            }
            expPricePartitionDTO.setDataList(dataList);

        }
        return partitionPage;
    }

    @Override
    public HashMap<String, Integer> queryShopPrice(Long channelId, String userId) {
//        UserChannelEntity userChannel = userChannelService.getMchChannel(userId,channelId);
        UserChannelDTO userChannel = new UserChannelDTO();
        Integer firstIncrease = userChannel.getFirstIncrease();
        Integer overIncrease = userChannel.getOverIncrease();
        HashMap<String, Integer> map = new HashMap<>();
        map.put("firstIncrease", firstIncrease);
        map.put("overIncrease", overIncrease);
        return map;
    }


    @Override
    public void insertExpPricePartition(ExpPricePartitionEntity expPricePartitionEntity) {
        expPricePartitionMapper.insert(expPricePartitionEntity);
        redisUtil.delete(PRICE_PARTITION_KEY_PREFIX + expPricePartitionEntity.getPid());
        redisUtil.removeByPrefix(String.format(PRICE_PID_START_END_PREFIX, expPricePartitionEntity.getPid()));
    }

    @Override
    public ExpPriceEntity getById(Long pid) {
        return expPriceMapper.selectById(pid);
    }

    @Override
    public ExpPricePartitionDTO queryCityAndCity(Long pid, BigDecimal weight, String originalCityCode, String destCityCode) {
        if (StrUtil.isBlank(originalCityCode) || StrUtil.isBlank(destCityCode)) {
            log.info("出发地编码或者目的地编码是null,{},{}", originalCityCode, destCityCode);
            return null;
        }
        ExpPricePartitionDTO expPricePartitionDTO = null;
        String redisKey = String.format(PRICE_PID_START_END, pid, originalCityCode, destCityCode);
        String value = redisUtil.get(redisKey);
        ExpPricePartitionEntity expPricePartitionEntity = null;
        if (StrUtil.isNotBlank(value)) {
            expPricePartitionEntity = JSONObject.parseObject(value, ExpPricePartitionEntity.class);
        } else {
            expPricePartitionEntity = expPriceMapper.queryCityAndCity(pid, originalCityCode, destCityCode);
            if (expPricePartitionEntity != null) {
                redisUtil.set(redisKey, JSONObject.toJSONString(expPricePartitionEntity), 60 * 60 * 24 * 7);
            }
        }
        if (expPricePartitionEntity != null) {
            //然后看看重量是在那个区间
            String computeSection = expPricePartitionEntity.getComputeSection();
            List<DetailPartition> detailPartitions = JSONObject.parseArray(computeSection, DetailPartition.class);
            expPricePartitionDTO = dealDetailPartition(detailPartitions, weight);
            //把计价空间放里面
            expPricePartitionDTO.setDataList(detailPartitions);
        }


        return expPricePartitionDTO;
    }


    //处理重量区间
    private ExpPricePartitionDTO dealDetailPartition(List<DetailPartition> detailPartitions, BigDecimal weight) {
        ExpPricePartitionDTO expPricePartitionDTO = new ExpPricePartitionDTO();
        for (DetailPartition detailPartition : detailPartitions) {
            Double maxWeight = detailPartition.getMaxWeight();
            Double minWeight = detailPartition.getMinWeight();
            if (BigDecimal.valueOf(minWeight).compareTo(weight) <= 0 && BigDecimal.valueOf(maxWeight).compareTo(weight) >= 0) {
                expPricePartitionDTO.setFirstPrice(detailPartition.getFirstPrice());
                //续重重量
                expPricePartitionDTO.setOverWeight(detailPartition.getOverWeight());
                expPricePartitionDTO.setFirstWeight(detailPartition.getFirstWeight());
                expPricePartitionDTO.setOverPrice(detailPartition.getEveryPrice());
            }
        }
        return expPricePartitionDTO;

    }

    @Override
    public List<ChargeRule> dealComputeSectionDiscount(List<DetailPartition> dataList) {
        //折扣计费
        List<ChargeRule> chargeRules = new ArrayList<>();
        if (dataList != null) {
            for (DetailPartition detailPartition : dataList) {
                ChargeRule chargeRule = new ChargeRule();
                chargeRule.setMinWeight(detailPartition.getMinWeight());
                chargeRule.setMaxWeight(detailPartition.getMaxWeight());
                chargeRule.setFirstPrice(detailPartition.getFirstPrice());
                chargeRule.setFirstWeight(detailPartition.getFirstWeight());
                chargeRule.setOverPrice(detailPartition.getEveryPrice());
                chargeRule.setOverWeight(detailPartition.getOverWeight());
                //加上支付类型
                chargeRules.add(chargeRule);
            }
        }

        return chargeRules;
    }

    @Override
    public List<ChargeRule> dealComputeSection(List<DetailPartition> dataList, UserChannelDTO channel, String tradeType) {
        //自定义计费
        List<ChargeRule> chargeRules = new ArrayList<>();
        if (dataList != null) {
            for (DetailPartition detailPartition : dataList) {
                ChargeRule chargeRule = new ChargeRule();
                chargeRule.setMinWeight(detailPartition.getMinWeight());
                chargeRule.setMaxWeight(detailPartition.getMaxWeight());
                //首重价格 = 自定义首重价格 + 首重加价
                chargeRule.setFirstPrice(detailPartition.getFirstPrice() + channel.getFirstIncrease());
                chargeRule.setFirstWeight(detailPartition.getFirstWeight());
                //续重价格 = 自定义续重价格 + 续重加价
                chargeRule.setOverPrice(detailPartition.getEveryPrice() + channel.getOverIncrease());
                chargeRule.setOverWeight(detailPartition.getOverWeight());
                //加上支付类型
                chargeRule.setTradeType(tradeType);
                chargeRules.add(chargeRule);
            }
        }

        return chargeRules;
    }

    @Override
    public Page<ExpPriceEntity> getPagePriceList(ExpPriceQuery query) {
        IPage<ExpPriceEntity> page = new Page<>(query.getPageNo(), query.getPageSize());
        // 取价格表数据
        QueryWrapper<ExpPriceEntity> qwPrice = new QueryWrapper<>();
        qwPrice.like(StringUtils.isNotBlank(query.getName()), "name", query.getName());
        IPage<ExpPriceEntity> iPage = expPriceMapper.selectPage(page, qwPrice);
        return new PageDTO<ExpPriceEntity>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal()).setRecords(iPage.getRecords());
    }

    @Override
    public List<ExpPriceEntity> getPriceList(Long userId) {
        LambdaQueryWrapper<ExpPriceEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ExpPriceEntity::getUserId, userId).or().eq(ExpPriceEntity::getIsCommon, 1);
        return expPriceMapper.selectList(wrapper);
    }

    @Override
    public List<ExpPriceEntity> getPriceByIds(List<Long> ids) {
        return expPriceMapper.selectBatchIds(ids);
    }

    @Override
    public PriceDto queryPrice(QueryPriceCommand param) {
        ExpUser expUser = expUserService.queryByName(param.getUserName());
        if (expUser == null) {
            throw new CustomException("未找到客户");
        }
        List<UserPriceVO> userPrice = userPriceService.getUserPrice(expUser.getId());
        List<PriceDto> collect = userPrice.stream().map(price -> getCostDiscount(param, price))
                .filter(price -> price != null && price.getDiscountPrice() != null)
                .sorted(Comparator.comparing(PriceDto::getDiscountPrice))
                .collect(Collectors.toList());
        if (collect.isEmpty()) {
            throw new CustomException("找不到价格");
        }
        if (param.getCompany() != null && !param.getCompany().equals("all")){
            collect = collect.stream().filter(price -> price.getCompany().contains(param.getCompany())).collect(Collectors.toList());
        }
        if (collect.isEmpty()) {
            throw new CustomException(String.format("未配置%s的价格",param.getCompany()));
        }
        if (collect.size() > 1) {
            PriceDto first = collect.get(0);
            PriceDto second = collect.get(1);
            first.setSecondCompany(second.getCompany());
            return first;
        } else {
            return collect.get(0);
        }
    }

    @Override
    public List<PriceDto> queryPriceList(QueryPriceCommand param) {
        ExpUser expUser = expUserService.queryByName(param.getUserName());
        if (expUser == null) {
            throw new CustomException("未找到客户");
        }
        List<UserPriceVO> userPrice = userPriceService.getUserPrice(expUser.getId());
        List<PriceDto> collect = userPrice.stream().map(price -> getCostDiscount(param, price))
                .filter(price -> price != null && price.getDiscountPrice() != null)
                .sorted(Comparator.comparing(PriceDto::getDiscountPrice))
                .collect(Collectors.toList());
        if (collect.isEmpty()) {
            throw new CustomException("找不到价格");
        }
        if (param.getCompany() != null && !param.getCompany().equals("all")){
            collect = collect.stream().filter(price -> price.getCompany().contains(param.getCompany())).collect(Collectors.toList());
        }
        if (collect.isEmpty()) {
            throw new CustomException(String.format("未配置%s的价格",param.getCompany()));
        }
        return collect;
    }

    private PriceDto getCostDiscount(QueryPriceCommand cmd, UserPriceVO expPriceEntity) {
        Long pid = expPriceEntity.getPriceId();
        BigDecimal weight = getWeight(cmd, expPriceEntity);
        Integer isDetail = expPriceEntity.getIsDetail();
        ExpPricePartitionDTO matchPartition = null;
        AddressVO origin = expProvinceService.queryByName(cmd.getOrigin());
        AddressVO dest = expProvinceService.queryByName(cmd.getDestination());
        log.info("出发地：{}，目的地:{}", JSON.toJSONString(origin), JSON.toJSONString(dest));
        //原来的省到省(不带价格区间)
        if (isDetail == 0) {
            List<ExpPricePartitionDTO> partitions = expPriceService.getPartitionByList(pid);
            if (partitions == null || partitions.isEmpty()) {
                throw new CustomException("找不到价格");
            }
            //根据收发件省份编码匹配分区
            matchPartition = partitions.stream()
                    .filter(partition -> partition.getOriginCodeList().contains(origin.getProvinceCode())
                            && partition.getDestCodeList().contains(dest.getProvinceCode()))
                    .findFirst()
                    .orElse(null);

        }
        if (isDetail == 1) {
            //市到市加价格区间
            matchPartition = expPriceService.queryCityAndCity(pid, weight, origin.getCityCode(), dest.getCityCode());
        }
        //省到省带价格区间
        if (isDetail == 2) {
            //省到省加价格区间
            // 先查询他的属于那个省份 同时封装成这个价格
            matchPartition = expPriceService.queryCityAndCity(pid, weight, origin.getProvinceCode(), dest.getProvinceCode());
        }
        //end
        //根据价格表id查询分区列表
        if (matchPartition == null) {
            log.error("未查询到价格分区列表，渠道：{}，查询参数：{}", JSONObject.toJSONString(expPriceEntity), JSONObject.toJSONString(cmd));
//            throw new CustomException("未查询到价格分区列表");
            return null;
        }
        //自定义计费
        CustomCalcFeeCommand customCalcCmd = new CustomCalcFeeCommand();

        //首重价格 = 自定义首重价格
        customCalcCmd.setFirstPrice(matchPartition.getFirstPrice());
        customCalcCmd.setFirstWeight(matchPartition.getFirstWeight());
        customCalcCmd.setLimitWeight(matchPartition.getLimitWeight());
        //续重价格 = 自定义续重价格
        customCalcCmd.setOverPrice(matchPartition.getOverPrice());
        customCalcCmd.setOverWeight(matchPartition.getOverWeight());
        customCalcCmd.setWeight(weight);
        customCalcCmd.setDiscount(expPriceEntity.getDiscount());
        //加上每个计价区间的值也要处理 start
        customCalcCmd.setComputeSection(expPriceService.dealComputeSectionDiscount(matchPartition.getDataList()));
        ExpressFeeDTO feeDTO = discountCalcFee(customCalcCmd);
        PriceDto dto = new PriceDto();
        dto.setWeight(weight);
        dto.setOriginPrice(convert(feeDTO.getOriginalPrice()));
        dto.setDiscountPrice(convert(feeDTO.getFreightFee()));
        dto.setOverPrice(convert(feeDTO.getOverPrice()));
        dto.setCompany(expPriceEntity.getCompanyName());
        if (isDetail == 0) {
            dto.setOrigin(origin.getProvinceName());
            dto.setDest(dest.getProvinceName());
        } else if (isDetail == 1) {
            dto.setOrigin(origin.getProvinceName() + origin.getCityName());
            dto.setDest(dest.getProvinceName() + dest.getCityName());
        } else {
            dto.setOrigin(origin.getProvinceName());
            dto.setDest(dest.getProvinceName());
        }
        dto.setTmpHead(expPriceEntity.getTmpHead());
        dto.setTmpBody(expPriceEntity.getTmpBody());
        dto.setTmpFoot(expPriceEntity.getTmpFoot());
        BigDecimal divide = BigDecimal.valueOf(expPriceEntity.getDiscount()).divide(BigDecimal.valueOf(10), 2, RoundingMode.UP);
        dto.setDiscountRate(divide);
        return dto;
    }

    private BigDecimal convert(Integer fee) {
        return BigDecimal.valueOf(fee).divide(BigDecimal.valueOf(100), 2, RoundingMode.UP);
    }

    @NotNull
    private static BigDecimal getWeight(QueryPriceCommand cmd, UserPriceVO expPriceEntity) {
        String type = cmd.getType();
        if (type == null) {
            throw new CustomException("重量识别错误1");
        }
        BigDecimal weight = BigDecimal.ZERO;
        if ("weight".equals(type)) {
            weight = new BigDecimal(cmd.getValue());
        } else if ("volume".equals(type)) {
            BigDecimal volume = new BigDecimal(cmd.getValue());
            Integer paobi = expPriceEntity.getPaobi();
            if (paobi == null) {
                throw new CustomException("未配置抛比");
            }
            //重量=体积/抛比，向上取整
            weight = volume.divide(new BigDecimal(paobi), 0, RoundingMode.UP);
        } else {
            throw new CustomException("重量识别错误2");
        }
        return weight;
    }

    public ExpressFeeDTO discountCalcFee(CustomCalcFeeCommand cmd) {
        BigDecimal price;

        if (cmd.getWeight().compareTo(BigDecimal.valueOf(cmd.getFirstWeight())) > 0) {
            //重量大于首重重量，价格 = 首重价格 + （重量（向上取整） - 首重重量）/ 续重重量 * 续重价格
            BigDecimal weight = cmd.getWeight().setScale(0, RoundingMode.UP)
                    .subtract(BigDecimal.valueOf(cmd.getFirstWeight()))
                    .divide(BigDecimal.valueOf(cmd.getOverWeight()));
            price = weight.multiply(BigDecimal.valueOf(cmd.getOverPrice())).add(BigDecimal.valueOf(cmd.getFirstPrice()));
        } else {
            //重量小于等于首重重量，价格 = 首重价格
            price = BigDecimal.valueOf(cmd.getFirstPrice());
        }

        //月结，返回自定义计算结果
        ExpressFeeDTO res = new ExpressFeeDTO();
        res.setCalWeight(cmd.getWeight());
        res.setOriginalPrice(price.intValue());
        res.setDiscountRate(cmd.getDiscount());
        res.setFirstPrice(cmd.getFirstPrice());
        res.setOverPrice(cmd.getOverPrice());
        res.setFirstWeight(cmd.getFirstWeight());
        res.setOverWeight(cmd.getOverWeight());
        res.setLimitWeight(cmd.getLimitWeight());
        //增加自定义规则
        List<ChargeRule> computeSection = cmd.getComputeSection();
        if (CollectionUtil.isNotEmpty(computeSection)) {
            res.setComputeSection(JSONObject.toJSONString(cmd.getComputeSection()));
        }
        //计算折扣后的费用;
        BigDecimal discountPrice = price.multiply(BigDecimal.valueOf(cmd.getDiscount())).divide(BigDecimal.valueOf(100), 0, RoundingMode.UP);
        res.setFreightFee(discountPrice.intValue());
        res.setAmount(discountPrice.intValue());
        return res;
    }


    @Override
    public List<String> getGroupList(){
        List<ExpUser> list = expUserService.list();
        return list.stream().map(ExpUser::getGroupName).collect(Collectors.toList());
    }
}
