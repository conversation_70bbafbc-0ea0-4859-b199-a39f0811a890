package com.longsheng.tools.modules.express.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longsheng.tools.modules.express.entity.ExpPriceEntity;
import com.longsheng.tools.modules.express.entity.ExpPricePartitionEntity;
import com.longsheng.tools.modules.express.params.ExpPartitionCommand;
import com.longsheng.tools.modules.express.params.ExpPriceCommand;
import com.longsheng.tools.modules.express.params.ExpPriceQuery;
import com.longsheng.tools.modules.express.params.QueryPriceCommand;
import com.longsheng.tools.modules.express.vo.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

public interface ExpPriceService {

    /**
     * 获取价格列表
     *
     * @param query
     * @return
     */
    List<ExpPriceEntity> getPriceList(ExpPriceQuery query);

    /**
     * 添加价格表
     *
     * @param command
     */
    void savePrice(ExpPriceCommand command);

    /**
     * 创建分区
     *
     * @param command
     */
    void createArea(ExpPartitionCommand command);

    /**
     * 修改分区
     *
     * @param command
     */
    void modifyArea(ExpPartitionCommand command);

    /**
     * 删除分区
     *
     * @param areaid
     */
    void deleteArea(long areaid);

    /**
     * 获取分区列表
     *
     * @param pid
     * @return
     */
    List<ExpPricePartitionDTO> getPartitionByList(long pid);


    /**
     * 后台查分页渠道
     *
     * @return
     */
    Page<ExpPricePartitionDTO> getPartitionPage(Long pid, ExpPriceQuery query);

    /**
     * 商户
     *
     * @return
     */
    Page<ExpPricePartitionDTO> shopPartitionPage(Long pid, Long channelId, String merchantId, ExpPriceQuery query);

    /**
     * 查询商户渠道加价
     *
     * @return
     */
    HashMap<String, Integer> queryShopPrice(Long channelId, String userId);

    /**
     * 插入分区价格
     */
    void insertExpPricePartition(ExpPricePartitionEntity expPricePartitionEntity);

    /**
     * 获取这个价格表的信息
     */
    ExpPriceEntity getById(Long pid);

    /**
     * 获取计价规则
     *
     * @param pid              价格表ID
     * @param weight           重量
     * @param originalCityCode 始发地code
     * @param destCityCode     目的地code
     * @return 计价规则
     */
    ExpPricePartitionDTO queryCityAndCity(Long pid, BigDecimal weight, String originalCityCode, String destCityCode);


    List<ChargeRule> dealComputeSectionDiscount(List<DetailPartition> dataList);

    List<ChargeRule> dealComputeSection(List<DetailPartition> dataList, UserChannelDTO channel, String tradeType);

    /**
     * 分页获取价格列表
     *
     * @param query
     * @return
     */

    Page<ExpPriceEntity> getPagePriceList(ExpPriceQuery query);


    List<ExpPriceEntity> getPriceList(Long userId);

    List<ExpPriceEntity> getPriceByIds(List<Long> ids);

    PriceDto queryPrice(QueryPriceCommand param);

    List<PriceDto> queryPriceList(QueryPriceCommand param);

    List<String> getGroupList();
}
