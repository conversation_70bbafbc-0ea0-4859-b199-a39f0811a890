spring:
  profiles:
    # 切换配置文件
    active: @profiles.active@

# sa-token权限认证管理
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: 'Authorization'
  # token风格
  token-style: 'tik'
  # token前缀
  token-prefix: 'Bearer'
  # token有效期，单位秒 默认30天，不支持自动续签
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒，支持自动续签
  activity-timeout: 36000
  # 自动续签，指定时间内有操作，则会自动续签
  auto-renew: true
  # 是否尝试从header里读取token
  is-read-header: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  # 是否尝试从请求体里读取token
  is-read-body: false
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 是否在初始化配置时打印版本字符画
  is-print: true
  # 是否输出操作日志
  is-log: true
  # 独立Redis插件
  alone-redis:
    host: ${spring.redis.host}
    port: ${spring.redis.port}
    password: ${spring.redis.password}
    # 数据库索引
    database: 2
    # 连接超时时间
    timeout: ${spring.redis.timeout}
    lettuce:
      pool:
        # 连接池中的最大空闲连接数
        max-idle: ${spring.redis.lettuce.pool.max-idle}
        # 连接池中的最小空闲连接数
        min-idle: ${spring.redis.lettuce.pool.min-idle}
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: ${spring.redis.lettuce.pool.max-active}
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: ${spring.redis.lettuce.pool.max-wait}
cos:
  tencent:
    secretId: AKIDMXVI1ZszGJBpKdkfu0uaxBD0b5SdbKwO
    secretKey: GH3JTSFrDNWUEk0wiIcjldFeu0wd6LJ7
    bucket: longshengwangluo-1306719837
    region: ap-nanjing
    allowPrefix: /quanshunquan
    baseUrl: https://longshengwangluo-1306719837.cos.ap-nanjing.myqcloud.com/
tencent:
  real-name:
    secret-id: AKID16mZhat6hko2947drs38leUh8yjmwl4onz5C
    secret-key: q7ah3ihiphxz6eyfz8al0rfr5ahi5dlB51max80


auth:
  skip:
    - /WXAppUser/login
    - /kaptcha-image
    - /login
    - /reset
    - /**/logout
    - /**/logout/*
    - /front/user/logout
    - /front/login
    - /front/login/register
    - /front/login/resetPassword
    - /front/login/validResetPasswordSmsCode
    - /front/login/updatePassword
    - /front/login/sendCode
    - /config/sys-app/getAppDownLoadUrl
    - /open/**
    - /api/**
    - /error
    - /favicon.ico
    - /**/*.html
    - /**/*.css
    - /**/*.js
    - /webservice/**
    - /ws/**
    - /**/queryEnableTools
    - /appEntry/getWechatLoginUrl
    - /appEntry/authCode/{code}
    - /promote/promote-business-param/checkPromoteBusinessParamId/{id}




tenant:
  ignore-urls:
    - /ws/**
    - /WXAppUser/login
    - /login # 基于名字获取租户，不许带租户编号
    - /logout
    - /appEntry/getWechatLoginUrl
    - /appEntry/authCode/{code}
    - /appEntry/getPersonInfo
    - /appEntry/authToken
    - /open/**
    - /promote/promote-business-param/getPromoteBusinessParamById/*
    - /promote/promote-business-param/checkPromoteBusinessParamId/{id}
    - /express/**
  ignore-tables:
    - auto_tools
    - gen_table
    - gen_table_column
    - sys_permission_info
    - sys_role_permission
    - sys_user_role
    - sys_role_info
    - sys_dict_detail
    - sys_dict
    - sys_param
    - exp_price
    - exp_price_dest
    - exp_price_origin
    - exp_price_partition
    - exp_province
    - exp_user
    - exp_user_price
    - sys_quartz_info
    - wechat_crash_log
