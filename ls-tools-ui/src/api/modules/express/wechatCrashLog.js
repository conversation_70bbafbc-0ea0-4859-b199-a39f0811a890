import request from '@/utils/request'

const baseUrl = '/express/wechat-crash-log/';

// 查询微信崩溃日志列表
export function listWechatCrashLog(query) {
  return request({
    url: baseUrl + 'list',
    method: 'get',
    params: query
  })
}

// 查询微信崩溃日志详细
export function getWechatCrashLog(id) {
  return request({
    url: baseUrl + id,
    method: 'get'
  })
}



// 根据IP地址查询崩溃日志
export function getWechatCrashLogByIp(ip) {
  return request({
    url: baseUrl + 'by-ip/' + ip,
    method: 'get'
  })
}

// 根据错误关键字查询崩溃日志
export function getWechatCrashLogByError(keyword) {
  return request({
    url: baseUrl + 'by-error/' + keyword,
    method: 'get'
  })
}
